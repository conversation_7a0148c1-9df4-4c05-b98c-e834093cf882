<?php
include 'db.php';

if(isset($_POST['course_id']) && isset($_FILES['videoFile'])){
    $course_id = intval($_POST['course_id']);
    $file_name = $_FILES['videoFile']['name'];
    $file_tmp = $_FILES['videoFile']['tmp_name'];

    $target_dir = "videos/";
    if(!is_dir($target_dir)) mkdir($target_dir, 0777, true);

    if(move_uploaded_file($file_tmp, $target_dir . $file_name)){
        $stmt = $conn->prepare("INSERT INTO videos (course_id, file_name) VALUES (:course_id, :file_name)");
        $stmt->execute(['course_id' => $course_id, 'file_name' => $file_name]);
        echo "تم رفع الفيديو بنجاح!";
    } else {
        echo "فشل رفع الفيديو.";
    }
}
?>
