<?php 
// تانايبلا ةدعاقب لاصتلاا تانايب 
$servername = 'localhost'; 
$username = 'root'; 
$password = ''; 
 
//  تانايبلا ةدعاقب لاصتا ءاشنإ 
$conn = mysqli_connect($servername, $username, $password); 
 
//  لاصتلاا حاجن نم ققحتلا 
if (!$conn) { 
    die('Connection failed: ' . mysqli_connect_error()); 
} 
 
//  لعفلاب ةدوجوم نكت مل اذإ تانايب ةدعاق ءاشنإ 
$sql_create_db = 'CREATE DATABASE IF NOT EXISTS sy'; 
if (mysqli_query($conn, $sql_create_db)) { 
    echo 'Database created successfully<br>'; 
} else { 
    echo 'Error creating database: ' . mysqli_error($conn); 
} 
 
$dbname = 'sy'; 
 
//  ةأشنملا تانايبلا ةدعاق مادختساب ديدج لاصتا ءاشنإ 
$conn = mysqli_connect($servername, $username, $password, $dbname); 
 
//  تلا ديدجلا لاصتلاا حاجن نم ققح 
if (!$conn) { 
    die('Connection failed: ' . mysqli_connect_error()); 
} 
 
// لودج ءاشنإ "persons"  لعفلاب اًدوجوم نكي مل اذإ 
$sql_create_table = "CREATE TABLE IF NOT EXISTS persons ( 
    person_name VARCHAR(50), 
    person_mobile VARCHAR(20) )"; 
if (mysqli_query($conn, $sql_create_table)) { 
    echo 'Table created successfully<br>'; 
} else { 
    echo 'Error creating table: ' . mysqli_error($conn); 
} 
 
//  لودجلا ىلإ صاخشلأا تانايب لاخدإ 
$sql_insert_data = "INSERT INTO persons (person_name, person_mobile) VALUES  
('Ahmad', '0933223311'), ('Saeed', '0943666666'), ('Maher', '0934444444')"; 
if (mysqli_multi_query($conn, $sql_insert_data)) { 
    echo 'New records created successfully<br>'; 
} else { 
    echo 'Error: ' . $sql_insert_data . '<br>' . mysqli_error($conn); 
} 
 
//  لودجلا نم تانايبلا دادرتسلا ملاعتسا 
$result = mysqli_query($conn, "SELECT * FROM persons WHERE person_mobile LIKE 
'093%'"); 
 
// لودج يف تانايبلا ضرع HTML 
$movie_header =<<<EOD
<h2><center>Review Database</center></h2> 
<table width='70%' border='1' cellpadding='2' align='center'> 
<tr> 
<td>Name</td> 
<td>Mobile</td> 
</tr> 
EOD; 
 
$movie_details = ''; 
while ($row = mysqli_fetch_assoc($result)) { 
    $firstname = $row['person_name']; 
    $lastname = $row['person_mobile']; 
    $movie_details .= <<<AAA 
    <tr> 
    <td>$firstname</td> 
    <td>$lastname</td> 
    </tr> 
AAA; 
} 
 
$movie_footer = '</table>'; 
$movie = <<<MOVIE 
$movie_header 
$movie_details 
$movie_footer 
MOVIE; 
 
echo $movie; 
 
// تانايبلا ةدعاق لاصتا قلاغإ 
