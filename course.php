<?php
include 'db.php'; // ربط قاعدة البيانات

$course_id = 1; // ضع هنا رقم الكورس الذي تريد عرضه
?>

<h1>فيديوهات الكورس</h1>
<video src="videos/1.mp4"></video>

<?php
$stmt = $conn->prepare("SELECT * FROM videos WHERE course_id = :course_id");
$stmt->execute(['course_id' => $course_id]);
$videos = $stmt->fetchAll(PDO::FETCH_ASSOC);

if(count($videos) == 0){
    echo "لا يوجد فيديوهات لهذا الكورس بعد.";
} else {
    foreach($videos as $video){
        echo '<video width="640" height="360" controls>
                <source src="videos/'.$video['file_name'].'" type="video/mp4">
                متصفحك لا يدعم تشغيل الفيديو.
              </video><br><br>';
    }
}
?>
